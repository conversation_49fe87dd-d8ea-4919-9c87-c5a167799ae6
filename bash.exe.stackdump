Stack trace:
Frame         Function      Args
0007FFFFB730  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA630) msys-2.0.dll+0x1FEBA
0007FFFFB730  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA08) msys-2.0.dll+0x67F9
0007FFFFB730  000210046832 (000210285FF9, 0007FFFFB5E8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB730  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB730  0002100690B4 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA10  00021006A49D (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF89DEB0000 ntdll.dll
7FF89D920000 KERNEL32.DLL
7FF89B580000 KERNELBASE.dll
7FF89C8E0000 USER32.dll
7FF89B550000 win32u.dll
7FF89C810000 GDI32.dll
7FF89B370000 gdi32full.dll
7FF89BAA0000 msvcp_win.dll
7FF89B0E0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF89DB70000 advapi32.dll
7FF89CAE0000 msvcrt.dll
7FF89BB40000 sechost.dll
7FF89B4A0000 bcrypt.dll
7FF89DD50000 RPCRT4.dll
7FF89A650000 CRYPTBASE.DLL
7FF89B4D0000 bcryptPrimitives.dll
7FF89CAA0000 IMM32.DLL
